import 'package:flutter/material.dart';
import 'package:my_fincance_app/models/category.dart';
import 'package:my_fincance_app/services/auth_service.dart';
import 'package:my_fincance_app/services/category_service.dart';
import 'package:provider/provider.dart';
import 'package:my_fincance_app/pages/auth/login_page.dart'; // Corrected import for LoginPage

class SettingsPage extends StatefulWidget {
  const SettingsPage({super.key});

  @override
  State<SettingsPage> createState() => _SettingsPageState();
}

class _SettingsPageState extends State<SettingsPage> with TickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _currentPasswordController = TextEditingController();
  final TextEditingController _newPasswordController = TextEditingController();
  final TextEditingController _confirmPasswordController = TextEditingController();
  final TextEditingController _categoryNameController = TextEditingController();
  
  String _selectedCategoryType = 'مصرف';
  bool _isEditingCategory = false;
  Category? _editingCategory;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    
    // Initialize name controller with current user name
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final authService = Provider.of<AuthService>(context, listen: false);
      if (authService.currentUser != null) {
        _nameController.text = authService.currentUser!.name;
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _nameController.dispose();
    _currentPasswordController.dispose();
    _newPasswordController.dispose();
    _confirmPasswordController.dispose();
    _categoryNameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'تنظیمات',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        centerTitle: true,
        backgroundColor: Colors.blue.shade50,
        elevation: 0,
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.blue,
          unselectedLabelColor: Colors.grey,
          indicatorColor: Colors.blue,
          isScrollable: true,
          tabs: const [
            Tab(
              icon: Icon(Icons.person),
              text: 'پروفایل',
            ),
            Tab(
              icon: Icon(Icons.category),
              text: 'دسته‌بندی‌ها',
            ),
            Tab(
              icon: Icon(Icons.display_settings),
              text: 'نمایش',
            ),
            Tab(
              icon: Icon(Icons.info),
              text: 'درباره',
            ),
          ],
        ),
      ),
      backgroundColor: Colors.grey.shade50,
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildProfileTab(),
          _buildCategoriesTab(),
          _buildDisplayTab(),
          _buildAboutTab(),
        ],
      ),
    );
  }

  Widget _buildProfileTab() {
    return Consumer<AuthService>(
      builder: (context, authService, child) {
        return SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Profile Header
              _buildProfileHeader(authService),
              const SizedBox(height: 24),

              // Profile Information Section
              _buildProfileInfoSection(authService),
              const SizedBox(height: 24),

              // Security Section
              _buildSecuritySection(),
              const SizedBox(height: 24),

              // Logout Section
              _buildLogoutSection(authService),
            ],
          ),
        );
      },
    );
  }

  Widget _buildProfileHeader(AuthService authService) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            colors: [Colors.blue.withValues(alpha: 0.1), Colors.blue.withValues(alpha: 0.05)],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            children: [
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: Colors.blue,
                  borderRadius: BorderRadius.circular(40),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.blue.withValues(alpha: 0.3),
                      blurRadius: 12,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: const Icon(
                  Icons.person,
                  size: 40,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 16),
              Text(
                authService.currentUser?.name ?? 'کاربر',
                style: const TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.blue,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                authService.currentUser?.email ?? '',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey.shade600,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProfileInfoSection(AuthService authService) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.edit, color: Colors.blue),
                SizedBox(width: 8),
                Text(
                  'ویرایش اطلاعات شخصی',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _nameController,
              decoration: InputDecoration(
                labelText: 'نام',
                prefixIcon: const Icon(Icons.person_outline),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                filled: true,
                fillColor: Colors.grey.shade50,
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () => _updateProfile(authService),
                icon: const Icon(Icons.save),
                label: const Text('ذخیره تغییرات'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSecuritySection() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.security, color: Colors.orange),
                SizedBox(width: 8),
                Text(
                  'امنیت',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ListTile(
              leading: const Icon(Icons.lock_outline, color: Colors.orange),
              title: const Text('تغییر رمز عبور'),
              subtitle: const Text('رمز عبور خود را تغییر دهید'),
              trailing: const Icon(Icons.arrow_forward_ios, size: 16),
              onTap: _showChangePasswordDialog,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLogoutSection(AuthService authService) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.exit_to_app, color: Colors.red),
                SizedBox(width: 8),
                Text(
                  'خروج از حساب',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () => _showLogoutDialog(authService),
                icon: const Icon(Icons.logout),
                label: const Text('خروج از حساب کاربری'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoriesTab() {
    return Consumer<CategoryService>(
      builder: (context, categoryService, child) {
        return SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Add Category Section
              _buildAddCategorySection(categoryService),
              const SizedBox(height: 24),

              // Categories List
              _buildCategoriesList(categoryService),
            ],
          ),
        );
      },
    );
  }

  Widget _buildAddCategorySection(CategoryService categoryService) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  _isEditingCategory ? Icons.edit : Icons.add,
                  color: Colors.blue,
                ),
                const SizedBox(width: 8),
                Text(
                  _isEditingCategory ? 'ویرایش دسته‌بندی' : 'افزودن دسته‌بندی جدید',
                  style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _categoryNameController,
              decoration: InputDecoration(
                labelText: 'نام دسته‌بندی',
                prefixIcon: const Icon(Icons.category_outlined),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                filled: true,
                fillColor: Colors.grey.shade50,
              ),
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<String>(
              value: _selectedCategoryType,
              decoration: InputDecoration(
                labelText: 'نوع دسته‌بندی',
                prefixIcon: const Icon(Icons.type_specimen),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                filled: true,
                fillColor: Colors.grey.shade50,
              ),
              items: const [
                DropdownMenuItem(value: 'درآمد', child: Text('درآمد')),
                DropdownMenuItem(value: 'مصرف', child: Text('مصرف')),
              ],
              onChanged: (value) {
                setState(() {
                  _selectedCategoryType = value!;
                });
              },
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                if (_isEditingCategory) ...[
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: _cancelCategoryEdit,
                      icon: const Icon(Icons.cancel),
                      label: const Text('لغو'),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: Colors.grey,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                ],
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _saveCategory(categoryService),
                    icon: Icon(_isEditingCategory ? Icons.save : Icons.add),
                    label: Text(_isEditingCategory ? 'ذخیره تغییرات' : 'افزودن'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoriesList(CategoryService categoryService) {
    final incomeCategories = categoryService.categories.where((c) => c.type == 'درآمد').toList();
    final expenseCategories = categoryService.categories.where((c) => c.type == 'مصرف').toList();

    return Column(
      children: [
        // Income Categories
        if (incomeCategories.isNotEmpty) ...[
          _buildCategorySection('دسته‌بندی‌های درآمد', incomeCategories, Colors.green),
          const SizedBox(height: 16),
        ],

        // Expense Categories
        if (expenseCategories.isNotEmpty) ...[
          _buildCategorySection('دسته‌بندی‌های مصرف', expenseCategories, Colors.red),
        ],
      ],
    );
  }

  Widget _buildCategorySection(String title, List<Category> categories, Color color) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.category, color: color),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '${categories.length} مورد',
                    style: TextStyle(
                      color: color,
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...categories.map((category) => _buildCategoryItem(category, color)),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryItem(Category category, Color color) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.2)),
      ),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(Icons.label, color: color, size: 20),
        ),
        title: Text(
          category.name,
          style: const TextStyle(fontWeight: FontWeight.w500),
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: const Icon(Icons.edit, color: Colors.blue),
              onPressed: () => _editCategory(category),
              tooltip: 'ویرایش',
            ),
            IconButton(
              icon: const Icon(Icons.delete, color: Colors.red),
              onPressed: () => _showDeleteCategoryDialog(category),
              tooltip: 'حذف',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDisplayTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Theme Section
          Card(
            elevation: 4,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Row(
                    children: [
                      Icon(Icons.palette, color: Colors.purple),
                      SizedBox(width: 8),
                      Text(
                        'ظاهر و نمایش',
                        style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  ListTile(
                    leading: const Icon(Icons.language, color: Colors.blue),
                    title: const Text('زبان'),
                    subtitle: const Text('فارسی (پیش‌فرض)'),
                    trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                    onTap: () {
                      _showSnackBar('زبان فارسی به عنوان پیش‌فرض تنظیم شده است', Colors.blue);
                    },
                  ),
                  const Divider(),
                  ListTile(
                    leading: const Icon(Icons.attach_money, color: Colors.green),
                    title: const Text('واحد پول'),
                    subtitle: const Text('افغانی (AFN)'),
                    trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                    onTap: () {
                      _showSnackBar('واحد پول افغانی به عنوان پیش‌فرض تنظیم شده است', Colors.green);
                    },
                  ),
                  const Divider(),
                  ListTile(
                    leading: const Icon(Icons.calendar_today, color: Colors.orange),
                    title: const Text('نوع تقویم'),
                    subtitle: const Text('تقویم شمسی (هجری شمسی)'),
                    trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                    onTap: () {
                      _showSnackBar('تقویم شمسی به عنوان پیش‌فرض تنظیم شده است', Colors.orange);
                    },
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAboutTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // App Info Section
          Card(
            elevation: 4,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            child: Padding(
              padding: const EdgeInsets.all(24.0),
              child: Column(
                children: [
                  Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      color: Colors.blue,
                      borderRadius: BorderRadius.circular(20),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.blue.withValues(alpha: 0.3),
                          blurRadius: 12,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: const Icon(
                      Icons.account_balance_wallet,
                      size: 40,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    'مرکز مالی من',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.blue,
                    ),
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'نسخه 1.0.0',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey,
                    ),
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    'اپلیکیشن جامع مدیریت امور مالی شخصی با پشتیبانی کامل از زبان فارسی و تقویم شمسی',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 14,
                      height: 1.5,
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          // Features Section
          Card(
            elevation: 4,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Row(
                    children: [
                      Icon(Icons.star, color: Colors.amber),
                      SizedBox(width: 8),
                      Text(
                        'ویژگی‌های اصلی',
                        style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  _buildFeatureItem(Icons.dashboard, 'داشبورد جامع مالی'),
                  _buildFeatureItem(Icons.receipt_long, 'مدیریت تراکنش‌ها'),
                  _buildFeatureItem(Icons.account_balance_wallet, 'پیگیری وام‌ها'),
                  _buildFeatureItem(Icons.pie_chart, 'بودجه‌بندی هوشمند'),
                  _buildFeatureItem(Icons.bar_chart, 'گزارش‌های تفصیلی'),
                  _buildFeatureItem(Icons.calendar_today, 'تقویم شمسی'),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          // Contact Section
          Card(
            elevation: 4,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Row(
                    children: [
                      Icon(Icons.contact_support, color: Colors.green),
                      SizedBox(width: 8),
                      Text(
                        'پشتیبانی و ارتباط',
                        style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  ListTile(
                    leading: const Icon(Icons.help, color: Colors.blue),
                    title: const Text('راهنمای کاربر'),
                    subtitle: const Text('مطالعه راهنمای کامل استفاده از برنامه'),
                    trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                    onTap: () {
                      _showSnackBar('راهنمای کاربر در بخش کمک موجود است', Colors.blue);
                    },
                  ),
                  const Divider(),
                  ListTile(
                    leading: const Icon(Icons.privacy_tip, color: Colors.purple),
                    title: const Text('حریم خصوصی'),
                    subtitle: const Text('اطلاعات شما به صورت محلی ذخیره می‌شود'),
                    trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                    onTap: () {
                      _showPrivacyDialog();
                    },
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFeatureItem(IconData icon, String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Icon(icon, color: Colors.blue, size: 20),
          const SizedBox(width: 12),
          Text(
            title,
            style: const TextStyle(fontSize: 14),
          ),
        ],
      ),
    );
  }

  void _updateProfile(AuthService authService) async {
    if (_nameController.text.trim().isEmpty) {
      _showSnackBar('نام نمی‌تواند خالی باشد', Colors.red);
      return;
    }

    final success = await authService.updateProfile(_nameController.text.trim());
    if (success) {
      _showSnackBar('اطلاعات با موفقیت به‌روزرسانی شد', Colors.green);
    } else {
      _showSnackBar('خطا در به‌روزرسانی اطلاعات', Colors.red);
    }
  }

  void _showChangePasswordDialog() {
    _currentPasswordController.clear();
    _newPasswordController.clear();
    _confirmPasswordController.clear();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تغییر رمز عبور'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: _currentPasswordController,
              obscureText: true,
              decoration: InputDecoration(
                labelText: 'رمز عبور فعلی',
                prefixIcon: const Icon(Icons.lock_outline),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _newPasswordController,
              obscureText: true,
              decoration: InputDecoration(
                labelText: 'رمز عبور جدید',
                prefixIcon: const Icon(Icons.lock),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _confirmPasswordController,
              obscureText: true,
              decoration: InputDecoration(
                labelText: 'تکرار رمز عبور جدید',
                prefixIcon: const Icon(Icons.lock),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('لغو'),
          ),
          ElevatedButton(
            onPressed: _changePassword,
            child: const Text('تغییر رمز'),
          ),
        ],
      ),
    );
  }

  void _changePassword() async {
    if (_currentPasswordController.text.isEmpty ||
        _newPasswordController.text.isEmpty ||
        _confirmPasswordController.text.isEmpty) {
      _showSnackBar('لطفاً همه فیلدها را پر کنید', Colors.red);
      return;
    }

    if (_newPasswordController.text != _confirmPasswordController.text) {
      _showSnackBar('رمز عبور جدید و تکرار آن یکسان نیستند', Colors.red);
      return;
    }

    if (_newPasswordController.text.length < 6) {
      _showSnackBar('رمز عبور باید حداقل ۶ کاراکتر باشد', Colors.red);
      return;
    }

    final authService = Provider.of<AuthService>(context, listen: false);
    final success = await authService.changePassword(
      _currentPasswordController.text,
      _newPasswordController.text,
    );

    if (success) {
      Navigator.of(context).pop();
      _showSnackBar('رمز عبور با موفقیت تغییر کرد', Colors.green);
    } else {
      _showSnackBar('رمز عبور فعلی اشتباه است', Colors.red);
    }
  }

  void _showLogoutDialog(AuthService authService) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('خروج از حساب'),
        content: const Text('آیا مطمئن هستید که می‌خواهید از حساب کاربری خود خارج شوید؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('لغو'),
          ),
          ElevatedButton(
            onPressed: () {
              authService.logout();
              Navigator.of(context).pop();
              Navigator.of(context).pushAndRemoveUntil(
                MaterialPageRoute(builder: (context) => const LoginPage()),
                (route) => false,
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('خروج', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  void _saveCategory(CategoryService categoryService) {
    if (_categoryNameController.text.trim().isEmpty) {
      _showSnackBar('نام دسته‌بندی نمی‌تواند خالی باشد', Colors.red);
      return;
    }

    if (_isEditingCategory && _editingCategory != null) {
      categoryService.updateCategory(
        _editingCategory!,
        _categoryNameController.text.trim(),
        _selectedCategoryType,
      );
      _showSnackBar('دسته‌بندی با موفقیت ویرایش شد', Colors.green);
    } else {
      categoryService.addCategory(
        _categoryNameController.text.trim(),
        _selectedCategoryType,
      );
      _showSnackBar('دسته‌بندی با موفقیت اضافه شد', Colors.green);
    }

    _cancelCategoryEdit();
  }

  void _editCategory(Category category) {
    setState(() {
      _isEditingCategory = true;
      _editingCategory = category;
      _categoryNameController.text = category.name;
      _selectedCategoryType = category.type;
    });
  }

  void _cancelCategoryEdit() {
    setState(() {
      _isEditingCategory = false;
      _editingCategory = null;
      _categoryNameController.clear();
      _selectedCategoryType = 'مصرف';
    });
  }

  void _showDeleteCategoryDialog(Category category) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف دسته‌بندی'),
        content: Text('آیا مطمئن هستید که می‌خواهید دسته‌بندی "${category.name}" را حذف کنید؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('لغو'),
          ),
          ElevatedButton(
            onPressed: () {
              final categoryService = Provider.of<CategoryService>(context, listen: false);
              categoryService.deleteCategory(category);
              Navigator.of(context).pop();
              _showSnackBar('دسته‌بندی با موفقیت حذف شد', Colors.green);
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حذف', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  void _showPrivacyDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حریم خصوصی'),
        content: const SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'امنیت اطلاعات شما:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 8),
              Text('• تمام اطلاعات شما به صورت محلی در دستگاه ذخیره می‌شود'),
              Text('• هیچ اطلاعاتی به سرور خارجی ارسال نمی‌شود'),
              Text('• رمز عبور شما به صورت رمزنگاری شده ذخیره می‌شود'),
              Text('• شما کنترل کامل بر روی اطلاعات خود دارید'),
              SizedBox(height: 16),
              Text(
                'ویژگی‌های امنیتی:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 8),
              Text('• رمزنگاری SHA-256 برای رمز عبور'),
              Text('• ذخیره‌سازی محلی با Hive Database'),
              Text('• عدم نیاز به اتصال اینترنت'),
              Text('• حفاظت از حریم خصوصی کاربر'),
            ],
          ),
        ),
        actions: [
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('متوجه شدم'),
          ),
        ],
      ),
    );
  }

  void _showSnackBar(String message, Color color) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: color,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        margin: const EdgeInsets.all(16),
      ),
    );
  }
}
