import 'package:hive/hive.dart';
import '../core/models/sync_status.dart';

part 'category.g.dart';

@HiveType(typeId: 2)
class Category extends HiveObject {
  @HiveField(0)
  late String id;

  @HiveField(1)
  late String name;

  @HiveField(2)
  late String type; // "درآمد" or "مصرف"

  @HiveField(3)
  late String userId;

  // Sync fields
  @HiveField(4)
  String? serverId;

  @HiveField(5)
  SyncStatus syncStatus = SyncStatus.pending;

  @HiveField(6)
  DateTime? lastSynced;

  @HiveField(7)
  late DateTime createdAt;

  @HiveField(8)
  late DateTime updatedAt;

  @HiveField(9)
  bool isDeleted = false;

  /// Convert to JSON for server sync
  Map<String, dynamic> toJson() {
    return {
      'id': serverId,
      'user_id': userId,
      'name': name,
      'type': type,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'last_synced': lastSynced?.toIso8601String(),
      'is_deleted': isDeleted,
    };
  }

  /// Update from server JSON
  void fromJson(Map<String, dynamic> json) {
    serverId = json['id'];
    userId = json['user_id'] ?? userId;
    name = json['name'] ?? name;
    type = json['type'] ?? type;
    if (json['created_at'] != null) {
      createdAt = DateTime.parse(json['created_at']);
    }
    if (json['updated_at'] != null) {
      updatedAt = DateTime.parse(json['updated_at']);
    }
    if (json['last_synced'] != null) {
      lastSynced = DateTime.parse(json['last_synced']);
    }
    isDeleted = json['is_deleted'] ?? false;
  }

  /// Mark as synced with server
  void markAsSynced(String serverUuid) {
    serverId = serverUuid;
    syncStatus = SyncStatus.synced;
    lastSynced = DateTime.now();
  }

  /// Mark as pending sync
  void markAsPending() {
    syncStatus = SyncStatus.pending;
    updatedAt = DateTime.now();
  }

  /// Mark as having sync conflict
  void markAsConflict() {
    syncStatus = SyncStatus.conflict;
  }

  /// Mark as sync error
  void markAsError() {
    syncStatus = SyncStatus.error;
  }

  /// Check if needs sync
  bool get needsSync =>
      syncStatus == SyncStatus.pending || syncStatus == SyncStatus.error;

  /// Check if has conflict
  bool get hasConflict => syncStatus == SyncStatus.conflict;

  /// Soft delete the record
  void softDelete() {
    isDeleted = true;
    markAsPending();
  }
}
