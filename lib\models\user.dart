import 'package:hive/hive.dart';
import '../core/models/sync_status.dart';

part 'user.g.dart';

@HiveType(typeId: 0)
class User extends HiveObject {
  @HiveField(0)
  late String email;

  @HiveField(1)
  late String password;

  @HiveField(2)
  late String name;

  // Sync fields
  @HiveField(3)
  late String id;

  @HiveField(4)
  String? serverId;

  @HiveField(5)
  SyncStatus syncStatus = SyncStatus.pending;

  @HiveField(6)
  DateTime? lastSynced;

  @HiveField(7)
  late DateTime createdAt;

  @HiveField(8)
  late DateTime updatedAt;

  @HiveField(9)
  bool isDeleted = false;

  @HiveField(10)
  late String userId; // For User model, this is the same as id

  /// Convert to JSON for server sync
  Map<String, dynamic> toJson() {
    return {
      'id': serverId,
      'email': email,
      'name': name,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'last_synced': lastSynced?.toIso8601String(),
    };
  }

  /// Update from server JSON
  void fromJson(Map<String, dynamic> json) {
    serverId = json['id'];
    email = json['email'] ?? email;
    name = json['name'] ?? name;
    if (json['created_at'] != null) {
      createdAt = DateTime.parse(json['created_at']);
    }
    if (json['updated_at'] != null) {
      updatedAt = DateTime.parse(json['updated_at']);
    }
    if (json['last_synced'] != null) {
      lastSynced = DateTime.parse(json['last_synced']);
    }
  }

  /// Mark as synced with server
  void markAsSynced(String serverUuid) {
    serverId = serverUuid;
    syncStatus = SyncStatus.synced;
    lastSynced = DateTime.now();
  }

  /// Mark as pending sync
  void markAsPending() {
    syncStatus = SyncStatus.pending;
    updatedAt = DateTime.now();
  }

  /// Mark as having sync conflict
  void markAsConflict() {
    syncStatus = SyncStatus.conflict;
  }

  /// Mark as sync error
  void markAsError() {
    syncStatus = SyncStatus.error;
  }

  /// Check if needs sync
  bool get needsSync =>
      syncStatus == SyncStatus.pending || syncStatus == SyncStatus.error;

  /// Check if has conflict
  bool get hasConflict => syncStatus == SyncStatus.conflict;

  /// Soft delete the record
  void softDelete() {
    isDeleted = true;
    markAsPending();
  }
}
