import 'package:flutter/foundation.dart';
import 'package:hive/hive.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../core/services/supabase_service.dart';

/// Base repository interface for all data repositories
abstract class BaseRepository<T extends HiveObject> {
  /// Get all items for a user
  Future<List<T>> getAll(String userId);

  /// Get item by ID
  Future<T?> getById(String id);

  /// Create new item
  Future<T> create(T item);

  /// Update existing item
  Future<T> update(T item);

  /// Delete item (soft delete)
  Future<void> delete(String id);

  /// Sync with remote server
  Future<void> sync(String userId);

  /// Get items that need sync
  Future<List<T>> getItemsNeedingSync(String userId);
}

/// Base implementation for repositories with local-first architecture
abstract class BaseRepositoryImpl<T extends HiveObject>
    implements BaseRepository<T> {
  final Box<T> localBox;
  final String tableName;

  BaseRepositoryImpl(this.localBox, this.tableName);

  /// Get the Supabase client
  SupabaseClient get supabase => SupabaseService.instance.client;

  /// Check if online
  bool get _isOnline => SupabaseService.instance.isOnline;

  /// Convert model to JSON for server sync
  Map<String, dynamic> toJson(T item);

  /// Create model from server JSON
  T fromJson(Map<String, dynamic> json, String userId);

  /// Get user ID from item
  String getUserId(T item);

  /// Get server ID from item
  String? getServerId(T item);

  /// Set server ID on item
  void setServerId(T item, String serverId);

  /// Mark item as synced
  void markAsSynced(T item, String serverId);

  /// Mark item as pending sync
  void markAsPending(T item);

  /// Check if item needs sync
  bool needsSync(T item);

  /// Check if item is deleted
  bool isDeleted(T item);

  /// Soft delete item
  void softDelete(T item);

  @override
  Future<List<T>> getAll(String userId) async {
    try {
      // Always return from local storage (Local-First)
      final items = localBox.values
          .where((item) => getUserId(item) == userId && !isDeleted(item))
          .toList();

      // Trigger background sync if online
      if (_isOnline) {
        _backgroundSync(userId);
      }

      return items;
    } catch (e) {
      throw Exception('Failed to get items: $e');
    }
  }

  @override
  Future<T?> getById(String id) async {
    try {
      final item = localBox.get(id);
      if (item != null && !isDeleted(item)) {
        return item;
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get item by ID: $e');
    }
  }

  @override
  Future<T> create(T item) async {
    try {
      // Set timestamps
      final now = DateTime.now();
      // These would be set on the actual item in concrete implementations

      // Save locally first (Local-First)
      await localBox.put(getItemId(item), item);

      // Mark as pending sync
      markAsPending(item);
      await item.save();

      // Try to sync immediately if online
      if (_isOnline) {
        try {
          await syncItemToServer(item);
        } catch (e) {
          // Sync failed, but item is saved locally
          debugPrint('Failed to sync new item to server: $e');
        }
      }

      return item;
    } catch (e) {
      throw Exception('Failed to create item: $e');
    }
  }

  @override
  Future<T> update(T item) async {
    try {
      // Update locally first (Local-First)
      markAsPending(item);
      await item.save();

      // Try to sync immediately if online
      if (_isOnline) {
        try {
          await syncItemToServer(item);
        } catch (e) {
          // Sync failed, but item is updated locally
          debugPrint('Failed to sync updated item to server: $e');
        }
      }

      return item;
    } catch (e) {
      throw Exception('Failed to update item: $e');
    }
  }

  @override
  Future<void> delete(String id) async {
    try {
      final item = localBox.get(id);
      if (item != null) {
        // Soft delete locally first (Local-First)
        softDelete(item);
        await item.save();

        // Try to sync deletion if online
        if (_isOnline) {
          try {
            await syncItemToServer(item);
          } catch (e) {
            // Sync failed, but item is deleted locally
            debugPrint('Failed to sync deleted item to server: $e');
          }
        }
      }
    } catch (e) {
      throw Exception('Failed to delete item: $e');
    }
  }

  @override
  Future<List<T>> getItemsNeedingSync(String userId) async {
    return localBox.values
        .where((item) => getUserId(item) == userId && needsSync(item))
        .toList();
  }

  /// Get item ID (abstract method to be implemented by concrete classes)
  String getItemId(T item);

  /// Background sync without blocking UI
  void _backgroundSync(String userId) {
    Future.microtask(() async {
      try {
        await sync(userId);
      } catch (e) {
        debugPrint('Background sync failed: $e');
      }
    });
  }

  /// Sync individual item to server
  Future<void> syncItemToServer(T item) async {
    if (!_isOnline) return;

    try {
      final json = toJson(item);
      final serverId = getServerId(item);

      if (isDeleted(item)) {
        // Delete on server
        if (serverId != null) {
          await supabase.from(tableName).delete().eq('id', serverId);
        }
        // Remove from local storage after successful server deletion
        await item.delete();
      } else if (serverId != null) {
        // Update on server
        final response = await supabase
            .from(tableName)
            .update(json)
            .eq('id', serverId)
            .select()
            .single();

        markAsSynced(item, response['id']);
        await item.save();
      } else {
        // Create on server
        final response = await supabase
            .from(tableName)
            .insert(json)
            .select()
            .single();

        markAsSynced(item, response['id']);
        await item.save();
      }
    } catch (e) {
      // Mark as error for retry later
      markAsError(item);
      await item.save();
      rethrow;
    }
  }

  /// Mark item as error
  void markAsError(T item);
}
