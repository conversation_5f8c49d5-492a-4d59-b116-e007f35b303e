import 'package:flutter/foundation.dart';
import 'package:hive/hive.dart';
import 'package:uuid/uuid.dart';
import '../../models/category.dart';
import '../../core/config/supabase_config.dart';
import '../../core/services/supabase_service.dart';
import 'base_repository.dart';

/// Repository for Category data with Local-First architecture
class CategoryRepository extends BaseRepositoryImpl<Category> {
  static final _uuid = Uuid();

  CategoryRepository(Box<Category> box)
    : super(box, SupabaseConfig.categoriesTable);

  @override
  Map<String, dynamic> toJson(Category item) => item.toJson();

  @override
  Category fromJson(Map<String, dynamic> json, String userId) {
    final category = Category()
      ..id = _uuid.v4()
      ..userId = userId
      ..createdAt = DateTime.now()
      ..updatedAt = DateTime.now();

    category.fromJson(json);
    return category;
  }

  @override
  String getUserId(Category item) => item.userId;

  @override
  String? getServerId(Category item) => item.serverId;

  @override
  void setServerId(Category item, String serverId) {
    item.serverId = serverId;
  }

  @override
  void markAsSynced(Category item, String serverId) {
    item.markAsSynced(serverId);
  }

  @override
  void markAsPending(Category item) {
    item.markAsPending();
  }

  @override
  bool needsSync(Category item) => item.needsSync;

  @override
  bool isDeleted(Category item) => item.isDeleted;

  @override
  void softDelete(Category item) {
    item.softDelete();
  }

  @override
  String getItemId(Category item) => item.id;

  @override
  void markAsError(Category item) {
    item.markAsError();
  }

  @override
  Future<void> sync(String userId) async {
    if (!_isOnline) return;

    try {
      // 1. Push local changes to server
      await _pushLocalChanges(userId);

      // 2. Pull server changes to local
      await _pullServerChanges(userId);
    } catch (e) {
      throw Exception('Sync failed: $e');
    }
  }

  /// Push local changes to server
  Future<void> _pushLocalChanges(String userId) async {
    final itemsToSync = await getItemsNeedingSync(userId);

    for (final item in itemsToSync) {
      try {
        await _syncItemToServer(item);
      } catch (e) {
        print('Failed to sync category ${item.id}: $e');
        // Continue with other items
      }
    }
  }

  /// Pull server changes to local
  Future<void> _pullServerChanges(String userId) async {
    try {
      final response = await _supabase
          .from(tableName)
          .select()
          .eq('user_id', userId)
          .order('updated_at', ascending: false);

      for (final serverData in response) {
        await _mergeServerItem(serverData, userId);
      }
    } catch (e) {
      throw Exception('Failed to pull server changes: $e');
    }
  }

  /// Merge server item with local data
  Future<void> _mergeServerItem(
    Map<String, dynamic> serverData,
    String userId,
  ) async {
    final serverId = serverData['id'];

    // Find existing local item by server ID
    Category? localItem = localBox.values
        .where((item) => item.serverId == serverId)
        .firstOrNull;

    if (localItem == null) {
      // Create new local item from server data
      localItem = fromJson(serverData, userId);
      await localBox.put(localItem.id, localItem);
    } else {
      // Check for conflicts
      final serverUpdated = DateTime.parse(serverData['updated_at']);
      final localUpdated = localItem.updatedAt;

      if (serverUpdated.isAfter(localUpdated) || !localItem.needsSync) {
        // Server version is newer or no local changes, update local
        localItem.fromJson(serverData);
        localItem.markAsSynced(serverId);
        await localItem.save();
      } else if (localItem.needsSync) {
        // Conflict: both local and server have changes
        // For now, keep local changes and mark as conflict
        localItem.markAsConflict();
        await localItem.save();
      }
    }
  }

  /// Get categories by type (income/expense)
  Future<List<Category>> getCategoriesByType(String userId, String type) async {
    final allCategories = await getAll(userId);
    return allCategories.where((c) => c.type == type).toList();
  }

  /// Get income categories
  Future<List<Category>> getIncomeCategories(String userId) async {
    return getCategoriesByType(userId, 'درآمد');
  }

  /// Get expense categories
  Future<List<Category>> getExpenseCategories(String userId) async {
    return getCategoriesByType(userId, 'مصرف');
  }

  /// Initialize default categories for a new user
  Future<void> initializeDefaultCategories(String userId) async {
    final existingCategories = await getAll(userId);
    if (existingCategories.isNotEmpty) return;

    // Default income categories
    final defaultIncomeCategories = [
      'معاش',
      'کسب و کار',
      'سرمایه‌گذاری',
      'هدیه',
      'سایر درآمدها',
    ];

    // Default expense categories
    final defaultExpenseCategories = [
      'غذا و نوشیدنی',
      'حمل و نقل',
      'خرید',
      'تفریح',
      'بهداشت و درمان',
      'آموزش',
      'خانه',
      'لباس',
      'سایر مصارف',
    ];

    final now = DateTime.now();

    // Create default income categories
    for (final categoryName in defaultIncomeCategories) {
      final category = Category()
        ..id = _uuid.v4()
        ..name = categoryName
        ..type = 'درآمد'
        ..userId = userId
        ..createdAt = now
        ..updatedAt = now;

      await create(category);
    }

    // Create default expense categories
    for (final categoryName in defaultExpenseCategories) {
      final category = Category()
        ..id = _uuid.v4()
        ..name = categoryName
        ..type = 'مصرف'
        ..userId = userId
        ..createdAt = now
        ..updatedAt = now;

      await create(category);
    }
  }

  /// Check if category name already exists for user
  Future<bool> categoryNameExists(
    String userId,
    String name, {
    String? excludeId,
  }) async {
    final categories = await getAll(userId);
    return categories.any(
      (c) =>
          c.name.toLowerCase() == name.toLowerCase() &&
          (excludeId == null || c.id != excludeId),
    );
  }
}
