import 'dart:convert';
import 'package:crypto/crypto.dart';
import 'package:flutter/foundation.dart';
import 'package:hive/hive.dart';
import 'package:my_fincance_app/models/user.dart';

class AuthService extends ChangeNotifier {
  final Box<User> _userBox = Hive.box<User>('users');
  final Box _settingsBox = Hive.box('settings');
  User? _currentUser;

  User? get currentUser => _currentUser;
  bool get isLoggedIn => _currentUser != null;
  String get currentUserId => _currentUser?.email ?? '';

  /// Hash password using SHA-256
  String _hashPassword(String password) {
    final bytes = utf8.encode(password);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  /// Register a new user
  Future<bool> register(String email, String password, String name) async {
    try {
      // Check if user already exists
      final userExists = _userBox.values.any((user) => user.email == email);

      if (userExists) {
        return false; // User already exists
      }

      // Create new user
      final newUser = User()
        ..email = email
        ..password = _hashPassword(password)
        ..name = name;

      await _userBox.put(email, newUser);
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Login user
  Future<bool> login(String email, String password) async {
    try {
      final user = _userBox.get(email);
      if (user != null && user.password == _hashPassword(password)) {
        _currentUser = user;
        // Save persistent login state
        _settingsBox.put('loggedInUserEmail', email);
        notifyListeners();
        return true;
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  /// Logout user
  void logout() {
    _currentUser = null;
    // Remove persistent login state
    _settingsBox.delete('loggedInUserEmail');
    notifyListeners();
  }

  /// Update user profile
  Future<bool> updateProfile(String name) async {
    try {
      if (_currentUser != null) {
        _currentUser!.name = name;
        await _currentUser!.save();
        notifyListeners();
        return true;
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  /// Change password
  Future<bool> changePassword(String currentPassword, String newPassword) async {
    try {
      if (_currentUser != null && 
          _currentUser!.password == _hashPassword(currentPassword)) {
        _currentUser!.password = _hashPassword(newPassword);
        await _currentUser!.save();
        return true;
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  /// Check if this is the first time opening the app
  bool get isFirstTime => _userBox.isEmpty;

  /// Auto-login for single user app (if only one user exists)
  void autoLogin() {
    final email = _settingsBox.get('loggedInUserEmail');
    if (email != null) {
      final user = _userBox.get(email);
      if (user != null) {
        _currentUser = user;
        notifyListeners();
      }
    }
  }
}
