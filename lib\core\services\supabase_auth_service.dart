import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:hive/hive.dart';
import '../../models/user.dart';
import 'supabase_service.dart';

/// Supabase authentication service that integrates with local Hive storage
class SupabaseAuthService extends ChangeNotifier {
  final Box<User> _userBox = Hive.box<User>('users');
  final Box _settingsBox = Hive.box('settings');
  
  User? _currentUser;
  bool _isInitialized = false;
  
  User? get currentUser => _currentUser;
  bool get isLoggedIn => _currentUser != null && SupabaseService.instance.client.auth.currentUser != null;
  String get currentUserId => _currentUser?.id ?? '';
  bool get isInitialized => _isInitialized;
  
  /// Initialize the auth service
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      // Listen to auth state changes
      SupabaseService.instance.client.auth.onAuthStateChange.listen((data) {
        _handleAuthStateChange(data.event, data.session);
      });
      
      // Check if user is already logged in
      final session = SupabaseService.instance.client.auth.currentSession;
      if (session != null) {
        await _loadUserFromSession(session);
      } else {
        // Try auto-login from local storage
        await _tryAutoLogin();
      }
      
      _isInitialized = true;
      notifyListeners();
    } catch (e) {
      debugPrint('Failed to initialize SupabaseAuthService: $e');
    }
  }
  
  /// Handle authentication state changes
  void _handleAuthStateChange(AuthChangeEvent event, Session? session) async {
    debugPrint('Auth state changed: $event');
    
    switch (event) {
      case AuthChangeEvent.signedIn:
        if (session != null) {
          await _loadUserFromSession(session);
        }
        break;
      case AuthChangeEvent.signedOut:
        await _handleSignOut();
        break;
      case AuthChangeEvent.userUpdated:
        if (session != null) {
          await _loadUserFromSession(session);
        }
        break;
      default:
        break;
    }
  }
  
  /// Load user from Supabase session
  Future<void> _loadUserFromSession(Session session) async {
    try {
      final supabaseUser = session.user;
      
      // Try to find existing local user
      User? localUser = _userBox.values.firstWhere(
        (user) => user.serverId == supabaseUser.id || user.email == supabaseUser.email,
        orElse: () => User(),
      );
      
      // If no local user found, create one
      if (localUser.id.isEmpty) {
        localUser = User()
          ..id = supabaseUser.id
          ..serverId = supabaseUser.id
          ..email = supabaseUser.email ?? ''
          ..name = supabaseUser.userMetadata?['name'] ?? 'User'
          ..password = '' // Not stored for Supabase users
          ..userId = supabaseUser.id
          ..createdAt = DateTime.now()
          ..updatedAt = DateTime.now()
          ..markAsSynced(supabaseUser.id);
        
        await _userBox.put(localUser.id, localUser);
      } else {
        // Update existing user
        localUser.serverId = supabaseUser.id;
        localUser.email = supabaseUser.email ?? localUser.email;
        localUser.name = supabaseUser.userMetadata?['name'] ?? localUser.name;
        localUser.markAsSynced(supabaseUser.id);
        await localUser.save();
      }
      
      _currentUser = localUser;
      _settingsBox.put('loggedInUserEmail', localUser.email);
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading user from session: $e');
    }
  }
  
  /// Handle sign out
  Future<void> _handleSignOut() async {
    _currentUser = null;
    _settingsBox.delete('loggedInUserEmail');
    notifyListeners();
  }
  
  /// Try to auto-login from local storage
  Future<void> _tryAutoLogin() async {
    final email = _settingsBox.get('loggedInUserEmail');
    if (email != null) {
      final user = _userBox.values.firstWhere(
        (user) => user.email == email,
        orElse: () => User(),
      );
      
      if (user.id.isNotEmpty && user.serverId != null) {
        // User exists locally and has server ID, try to refresh session
        try {
          await SupabaseService.instance.client.auth.refreshSession();
        } catch (e) {
          debugPrint('Failed to refresh session: $e');
        }
      }
    }
  }
  
  /// Register a new user with Supabase
  Future<bool> register(String email, String password, String name) async {
    try {
      if (!SupabaseService.instance.isOnline) {
        throw Exception('No internet connection');
      }
      
      final response = await SupabaseService.instance.client.auth.signUp(
        email: email,
        password: password,
        data: {'name': name},
      );
      
      if (response.user != null) {
        // User will be loaded via auth state change
        return true;
      }
      
      return false;
    } catch (e) {
      debugPrint('Registration failed: $e');
      return false;
    }
  }
  
  /// Login user with Supabase
  Future<bool> login(String email, String password) async {
    try {
      if (!SupabaseService.instance.isOnline) {
        // Try local login for offline mode
        return await _loginLocally(email, password);
      }
      
      final response = await SupabaseService.instance.client.auth.signInWithPassword(
        email: email,
        password: password,
      );
      
      if (response.user != null) {
        // User will be loaded via auth state change
        return true;
      }
      
      return false;
    } catch (e) {
      debugPrint('Login failed: $e');
      // Try local login as fallback
      return await _loginLocally(email, password);
    }
  }
  
  /// Local login for offline mode
  Future<bool> _loginLocally(String email, String password) async {
    try {
      final user = _userBox.values.firstWhere(
        (user) => user.email == email,
        orElse: () => User(),
      );
      
      if (user.id.isNotEmpty && user.password.isNotEmpty) {
        // For local users with password
        final hashedPassword = _hashPassword(password);
        if (user.password == hashedPassword) {
          _currentUser = user;
          _settingsBox.put('loggedInUserEmail', email);
          notifyListeners();
          return true;
        }
      }
      
      return false;
    } catch (e) {
      debugPrint('Local login failed: $e');
      return false;
    }
  }
  
  /// Hash password for local storage
  String _hashPassword(String password) {
    // Use the same hashing as the original AuthService
    return password; // Simplified for now, should use proper hashing
  }
  
  /// Logout user
  Future<void> logout() async {
    try {
      if (SupabaseService.instance.isOnline) {
        await SupabaseService.instance.client.auth.signOut();
      } else {
        await _handleSignOut();
      }
    } catch (e) {
      debugPrint('Logout failed: $e');
      await _handleSignOut();
    }
  }
  
  /// Update user profile
  Future<bool> updateProfile(String name) async {
    try {
      if (_currentUser == null) return false;
      
      if (SupabaseService.instance.isOnline && _currentUser!.serverId != null) {
        // Update on server
        await SupabaseService.instance.client.auth.updateUser(
          UserAttributes(data: {'name': name}),
        );
      }
      
      // Update locally
      _currentUser!.name = name;
      _currentUser!.markAsPending();
      await _currentUser!.save();
      notifyListeners();
      
      return true;
    } catch (e) {
      debugPrint('Profile update failed: $e');
      return false;
    }
  }
  
  /// Check if this is the first time opening the app
  bool get isFirstTime => _userBox.isEmpty;
}
