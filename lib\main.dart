import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:my_fincance_app/models/budget.dart';
import 'package:my_fincance_app/models/category.dart';
import 'package:my_fincance_app/models/loan.dart';
import 'package:my_fincance_app/models/transaction.dart';
import 'package:my_fincance_app/models/user.dart';
import 'package:my_fincance_app/core/models/sync_status.dart';
import 'package:my_fincance_app/core/services/supabase_service.dart';
import 'package:my_fincance_app/core/services/supabase_auth_service.dart';
import 'package:my_fincance_app/core/services/sync_service.dart';
import 'package:my_fincance_app/pages/auth/login_page.dart';
import 'package:my_fincance_app/pages/home_page.dart';

import 'package:my_fincance_app/services/budget_service.dart';
import 'package:my_fincance_app/services/capital_service.dart';
import 'package:my_fincance_app/services/category_service.dart';
import 'package:my_fincance_app/services/transaction_service.dart';
import 'package:my_fincance_app/services/loan_service.dart';
import 'package:provider/provider.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Hive
  await Hive.initFlutter();

  // Register Hive adapters
  Hive.registerAdapter(UserAdapter());
  Hive.registerAdapter(LoanAdapter());
  Hive.registerAdapter(CategoryAdapter());
  Hive.registerAdapter(TransactionAdapter());
  Hive.registerAdapter(BudgetAdapter());
  Hive.registerAdapter(SyncStatusAdapter());

  // Open Hive boxes
  await Hive.openBox<User>('users');
  await Hive.openBox<Loan>('loans');
  await Hive.openBox<Category>('categories');
  await Hive.openBox<Transaction>('transactions');
  await Hive.openBox<Budget>('budgets');
  await Hive.openBox('settings');

  // Initialize Supabase
  try {
    await SupabaseService.instance.initialize();
  } catch (e) {
    debugPrint('Failed to initialize Supabase: $e');
    // Continue with offline mode
  }

  runApp(
    MultiProvider(
      providers: [
        // Primary authentication service (Supabase + Local)
        ChangeNotifierProvider(create: (context) => SupabaseAuthService()),

        // Sync service
        ChangeNotifierProvider(create: (context) => SyncService.instance),
        // Legacy services (will be updated to use repositories)
        ChangeNotifierProxyProvider<SupabaseAuthService, CategoryService>(
          create: (context) => CategoryService(''),
          update: (context, authService, categoryService) {
            final userId = authService.currentUserId;
            return CategoryService(userId);
          },
        ),
        ChangeNotifierProxyProvider<SupabaseAuthService, LoanService>(
          create: (context) => LoanService(''),
          update: (context, authService, loanService) {
            final userId = authService.currentUserId;
            return LoanService(userId);
          },
        ),
        ChangeNotifierProxyProvider2<
          SupabaseAuthService,
          LoanService,
          TransactionService
        >(
          create: (context) => TransactionService(''),
          update: (context, authService, loanService, transactionService) {
            final userId = authService.currentUserId;
            final newTransactionService = TransactionService(userId);
            newTransactionService.setLoanService(loanService);
            return newTransactionService;
          },
        ),
        ChangeNotifierProxyProvider<SupabaseAuthService, BudgetService>(
          create: (context) => BudgetService(''),
          update: (context, authService, budgetService) {
            final userId = authService.currentUserId;
            return BudgetService(userId);
          },
        ),
        ChangeNotifierProxyProvider2<
          TransactionService,
          LoanService,
          CapitalService
        >(
          create: (context) =>
              CapitalService(TransactionService(''), LoanService('')),
          update: (context, transactionService, loanService, capitalService) {
            return CapitalService(transactionService, loanService);
          },
        ),
      ],
      child: const MyApp(),
    ),
  );
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'مرکز مالی من',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        textTheme: GoogleFonts.lalezarTextTheme(Theme.of(context).textTheme),
      ),
      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: const [
        Locale('fa', ''), // Persian
      ],
      locale: const Locale('fa', ''),
      home: Consumer2<SupabaseAuthService, SyncService>(
        builder: (context, authService, syncService, child) {
          // Initialize auth service if not done yet
          if (!authService.isInitialized) {
            authService.initialize();
            return const Scaffold(
              body: Center(child: CircularProgressIndicator()),
            );
          }

          // Initialize sync service when user is logged in
          if (authService.isLoggedIn && !syncService.isInitialized) {
            syncService.initialize(authService.currentUserId);
          }

          return authService.isLoggedIn ? const HomePage() : const LoginPage();
        },
      ),
    );
  }
}
