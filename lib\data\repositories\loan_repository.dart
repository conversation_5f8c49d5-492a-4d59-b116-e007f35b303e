import 'package:flutter/foundation.dart';
import 'package:hive/hive.dart';
import 'package:uuid/uuid.dart';
import '../../models/loan.dart';
import '../../core/config/supabase_config.dart';
import '../../core/services/supabase_service.dart';
import 'base_repository.dart';

/// Repository for Loan data with Local-First architecture
class LoanRepository extends BaseRepositoryImpl<Loan> {
  static final _uuid = Uuid();

  LoanRepository(Box<Loan> box) : super(box, SupabaseConfig.loansTable);

  @override
  Map<String, dynamic> toJson(Loan item) => item.toJson();

  @override
  Loan fromJson(Map<String, dynamic> json, String userId) {
    final loan = Loan()
      ..id = _uuid.v4()
      ..userId = userId
      ..createdAt = DateTime.now()
      ..updatedAt = DateTime.now();

    loan.fromJson(json);
    return loan;
  }

  @override
  String getUserId(Loan item) => item.userId;

  @override
  String? getServerId(Loan item) => item.serverId;

  @override
  void setServerId(Loan item, String serverId) {
    item.serverId = serverId;
  }

  @override
  void markAsSynced(Loan item, String serverId) {
    item.markAsSynced(serverId);
  }

  @override
  void markAsPending(Loan item) {
    item.markAsPending();
  }

  @override
  bool needsSync(Loan item) => item.needsSync;

  @override
  bool isDeleted(Loan item) => item.isDeleted;

  @override
  void softDelete(Loan item) {
    item.softDelete();
  }

  @override
  String getItemId(Loan item) => item.id;

  @override
  void markAsError(Loan item) {
    item.markAsError();
  }

  @override
  Future<void> sync(String userId) async {
    if (!SupabaseService.instance.isOnline) return;

    try {
      // 1. Push local changes to server
      await _pushLocalChanges(userId);

      // 2. Pull server changes to local
      await _pullServerChanges(userId);
    } catch (e) {
      throw Exception('Sync failed: $e');
    }
  }

  /// Push local changes to server
  Future<void> _pushLocalChanges(String userId) async {
    final itemsToSync = await getItemsNeedingSync(userId);

    for (final item in itemsToSync) {
      try {
        await syncItemToServer(item);
      } catch (e) {
        debugPrint('Failed to sync loan ${item.id}: $e');
        // Continue with other items
      }
    }
  }

  /// Pull server changes to local
  Future<void> _pullServerChanges(String userId) async {
    try {
      final response = await supabase
          .from(tableName)
          .select()
          .eq('user_id', userId)
          .order('updated_at', ascending: false);

      for (final serverData in response) {
        await _mergeServerItem(serverData, userId);
      }
    } catch (e) {
      throw Exception('Failed to pull server changes: $e');
    }
  }

  /// Merge server item with local data
  Future<void> _mergeServerItem(
    Map<String, dynamic> serverData,
    String userId,
  ) async {
    final serverId = serverData['id'];

    // Find existing local item by server ID
    Loan? localItem = localBox.values
        .where((item) => item.serverId == serverId)
        .firstOrNull;

    if (localItem == null) {
      // Create new local item from server data
      localItem = fromJson(serverData, userId);
      await localBox.put(localItem.id, localItem);
    } else {
      // Check for conflicts
      final serverUpdated = DateTime.parse(serverData['updated_at']);
      final localUpdated = localItem.updatedAt;

      if (serverUpdated.isAfter(localUpdated) || !localItem.needsSync) {
        // Server version is newer or no local changes, update local
        localItem.fromJson(serverData);
        localItem.markAsSynced(serverId);
        await localItem.save();
      } else if (localItem.needsSync) {
        // Conflict: both local and server have changes
        // For now, keep local changes and mark as conflict
        localItem.markAsConflict();
        await localItem.save();
      }
    }
  }

  /// Get loans by type (debt/credit)
  Future<List<Loan>> getLoansByType(String userId, String type) async {
    final allLoans = await getAll(userId);
    return allLoans.where((l) => l.type == type).toList();
  }

  /// Get debt loans
  Future<List<Loan>> getDebtLoans(String userId) async {
    return getLoansByType(userId, 'بدهی');
  }

  /// Get credit loans
  Future<List<Loan>> getCreditLoans(String userId) async {
    return getLoansByType(userId, 'طلب');
  }

  /// Get active loans
  Future<List<Loan>> getActiveLoans(String userId) async {
    final allLoans = await getAll(userId);
    return allLoans.where((l) => l.status == 'فعال').toList();
  }

  /// Get completed loans
  Future<List<Loan>> getCompletedLoans(String userId) async {
    final allLoans = await getAll(userId);
    return allLoans.where((l) => l.status == 'تمام شده').toList();
  }

  /// Get loans by status
  Future<List<Loan>> getLoansByStatus(String userId, String status) async {
    final allLoans = await getAll(userId);
    return allLoans.where((l) => l.status == status).toList();
  }

  /// Calculate total debt amount
  Future<double> getTotalDebtAmount(String userId) async {
    final debtLoans = await getDebtLoans(userId);
    final activeDebts = debtLoans.where((l) => l.status == 'فعال');
    return activeDebts.fold<double>(
      0.0,
      (sum, loan) => sum + loan.initialAmount,
    );
  }

  /// Calculate total credit amount
  Future<double> getTotalCreditAmount(String userId) async {
    final creditLoans = await getCreditLoans(userId);
    final activeCredits = creditLoans.where((l) => l.status == 'فعال');
    return activeCredits.fold<double>(
      0.0,
      (sum, loan) => sum + loan.initialAmount,
    );
  }

  /// Get loans summary
  Future<Map<String, dynamic>> getLoansSummary(String userId) async {
    final allLoans = await getAll(userId);
    final activeLoans = allLoans.where((l) => l.status == 'فعال');
    final completedLoans = allLoans.where((l) => l.status == 'تمام شده');

    final activeDebts = activeLoans.where((l) => l.type == 'بدهی');
    final activeCredits = activeLoans.where((l) => l.type == 'طلب');

    final totalDebt = activeDebts.fold<double>(
      0.0,
      (sum, loan) => sum + loan.initialAmount,
    );
    final totalCredit = activeCredits.fold<double>(
      0.0,
      (sum, loan) => sum + loan.initialAmount,
    );

    return {
      'totalLoans': allLoans.length,
      'activeLoans': activeLoans.length,
      'completedLoans': completedLoans.length,
      'totalDebt': totalDebt,
      'totalCredit': totalCredit,
      'netPosition': totalCredit - totalDebt,
    };
  }

  /// Update loan status
  Future<Loan> updateLoanStatus(String loanId, String newStatus) async {
    final loan = await getById(loanId);
    if (loan != null) {
      loan.status = newStatus;
      return await update(loan);
    }
    throw Exception('Loan not found');
  }

  /// Search loans by person name
  Future<List<Loan>> searchLoansByPerson(
    String userId,
    String personName,
  ) async {
    final allLoans = await getAll(userId);
    return allLoans
        .where((l) => l.person.toLowerCase().contains(personName.toLowerCase()))
        .toList();
  }
}
