import 'package:hive/hive.dart';

part 'sync_status.g.dart';

/// Enum to track synchronization status of local data
@HiveType(typeId: 10)
enum SyncStatus {
  @HiveField(0)
  synced, // Data is synchronized with server

  @HiveField(1)
  pending, // Data needs to be uploaded to server

  @HiveField(2)
  conflict, // Data has conflicts that need resolution

  @HiveField(3)
  error, // Sync failed due to error
}

/// Mixin for all syncable models
mixin SyncableModel {
  /// Local unique identifier
  late String id;

  /// Server-side UUID (null if not synced yet)
  String? serverId;

  /// Current sync status
  SyncStatus syncStatus = SyncStatus.pending;

  /// Timestamp when last synced with server
  DateTime? lastSynced;

  /// Timestamp when locally created
  late DateTime createdAt;

  /// Timestamp when locally updated
  late DateTime updatedAt;

  /// Soft delete flag
  bool isDeleted = false;

  /// User ID this record belongs to
  late String userId;

  /// Convert to JSON for server sync
  Map<String, dynamic> toJson();

  /// Update from server JSON
  void fromJson(Map<String, dynamic> json);

  /// Mark as synced with server
  void markAsSynced(String serverUuid) {
    serverId = serverUuid;
    syncStatus = SyncStatus.synced;
    lastSynced = DateTime.now();
  }

  /// Mark as pending sync
  void markAsPending() {
    syncStatus = SyncStatus.pending;
    updatedAt = DateTime.now();
  }

  /// Mark as having sync conflict
  void markAsConflict() {
    syncStatus = SyncStatus.conflict;
  }

  /// Mark as sync error
  void markAsError() {
    syncStatus = SyncStatus.error;
  }

  /// Check if needs sync
  bool get needsSync =>
      syncStatus == SyncStatus.pending || syncStatus == SyncStatus.error;

  /// Check if has conflict
  bool get hasConflict => syncStatus == SyncStatus.conflict;

  /// Soft delete the record
  void softDelete() {
    isDeleted = true;
    markAsPending();
  }
}
