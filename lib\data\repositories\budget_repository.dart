import 'package:hive/hive.dart';
import 'package:uuid/uuid.dart';
import '../../models/budget.dart';
import '../../core/config/supabase_config.dart';
import 'base_repository.dart';

/// Repository for Budget data with Local-First architecture
class BudgetRepository extends BaseRepositoryImpl<Budget> {
  static final _uuid = Uuid();
  
  BudgetRepository(Box<Budget> box) 
      : super(box, SupabaseConfig.budgetsTable);
  
  @override
  Map<String, dynamic> toJson(Budget item) => item.toJson();
  
  @override
  Budget fromJson(Map<String, dynamic> json, String userId) {
    final budget = Budget()
      ..id = _uuid.v4()
      ..userId = userId
      ..createdAt = DateTime.now()
      ..updatedAt = DateTime.now();
    
    budget.fromJson(json);
    return budget;
  }
  
  @override
  String getUserId(Budget item) => item.userId;
  
  @override
  String? getServerId(Budget item) => item.serverId;
  
  @override
  void setServerId(Budget item, String serverId) {
    item.serverId = serverId;
  }
  
  @override
  void markAsSynced(Budget item, String serverId) {
    item.markAsSynced(serverId);
  }
  
  @override
  void markAsPending(Budget item) {
    item.markAsPending();
  }
  
  @override
  bool needsSync(Budget item) => item.needsSync;
  
  @override
  bool isDeleted(Budget item) => item.isDeleted;
  
  @override
  void softDelete(Budget item) {
    item.softDelete();
  }
  
  @override
  String getItemId(Budget item) => item.id;
  
  @override
  void markAsError(Budget item) {
    item.markAsError();
  }
  
  @override
  Future<void> sync(String userId) async {
    if (!_isOnline) return;
    
    try {
      // 1. Push local changes to server
      await _pushLocalChanges(userId);
      
      // 2. Pull server changes to local
      await _pullServerChanges(userId);
      
    } catch (e) {
      throw Exception('Sync failed: $e');
    }
  }
  
  /// Push local changes to server
  Future<void> _pushLocalChanges(String userId) async {
    final itemsToSync = await getItemsNeedingSync(userId);
    
    for (final item in itemsToSync) {
      try {
        await _syncItemToServer(item);
      } catch (e) {
        print('Failed to sync budget ${item.id}: $e');
        // Continue with other items
      }
    }
  }
  
  /// Pull server changes to local
  Future<void> _pullServerChanges(String userId) async {
    try {
      final response = await _supabase
          .from(tableName)
          .select()
          .eq('user_id', userId)
          .order('updated_at', ascending: false);
      
      for (final serverData in response) {
        await _mergeServerItem(serverData, userId);
      }
    } catch (e) {
      throw Exception('Failed to pull server changes: $e');
    }
  }
  
  /// Merge server item with local data
  Future<void> _mergeServerItem(Map<String, dynamic> serverData, String userId) async {
    final serverId = serverData['id'];
    
    // Find existing local item by server ID
    Budget? localItem = localBox.values
        .where((item) => item.serverId == serverId)
        .firstOrNull;
    
    if (localItem == null) {
      // Create new local item from server data
      localItem = fromJson(serverData, userId);
      await localBox.put(localItem.id, localItem);
    } else {
      // Check for conflicts
      final serverUpdated = DateTime.parse(serverData['updated_at']);
      final localUpdated = localItem.updatedAt;
      
      if (serverUpdated.isAfter(localUpdated) || !localItem.needsSync) {
        // Server version is newer or no local changes, update local
        localItem.fromJson(serverData);
        localItem.markAsSynced(serverId);
        await localItem.save();
      } else if (localItem.needsSync) {
        // Conflict: both local and server have changes
        // For now, keep local changes and mark as conflict
        localItem.markAsConflict();
        await localItem.save();
      }
    }
  }
  
  /// Get budgets for a specific period (month)
  Future<List<Budget>> getBudgetsForPeriod(String userId, DateTime period) async {
    final allBudgets = await getAll(userId);
    final firstDayOfMonth = DateTime(period.year, period.month, 1);
    return allBudgets.where((b) => 
      b.period.year == firstDayOfMonth.year && 
      b.period.month == firstDayOfMonth.month
    ).toList();
  }
  
  /// Get current month budgets
  Future<List<Budget>> getCurrentMonthBudgets(String userId) async {
    final now = DateTime.now();
    return getBudgetsForPeriod(userId, now);
  }
  
  /// Get budget for specific category and period
  Future<Budget?> getBudgetForCategoryAndPeriod(String userId, String categoryId, DateTime period) async {
    final periodBudgets = await getBudgetsForPeriod(userId, period);
    return periodBudgets.where((b) => b.categoryId == categoryId).firstOrNull;
  }
  
  /// Get budgets by category
  Future<List<Budget>> getBudgetsByCategory(String userId, String categoryId) async {
    final allBudgets = await getAll(userId);
    return allBudgets.where((b) => b.categoryId == categoryId).toList();
  }
  
  /// Create or update budget for category and period
  Future<Budget> createOrUpdateBudget(String userId, String categoryId, DateTime period, double amount) async {
    final firstDayOfMonth = DateTime(period.year, period.month, 1);
    final existingBudget = await getBudgetForCategoryAndPeriod(userId, categoryId, firstDayOfMonth);
    
    if (existingBudget != null) {
      // Update existing budget
      existingBudget.amount = amount;
      return await update(existingBudget);
    } else {
      // Create new budget
      final newBudget = Budget()
        ..id = _uuid.v4()
        ..userId = userId
        ..categoryId = categoryId
        ..period = firstDayOfMonth
        ..amount = amount
        ..createdAt = DateTime.now()
        ..updatedAt = DateTime.now();
      
      return await create(newBudget);
    }
  }
  
  /// Get budget summary for a period
  Future<Map<String, dynamic>> getBudgetSummary(String userId, DateTime period) async {
    final budgets = await getBudgetsForPeriod(userId, period);
    
    final totalBudget = budgets.fold<double>(0.0, (sum, b) => sum + b.amount);
    final budgetCount = budgets.length;
    
    return {
      'totalBudget': totalBudget,
      'budgetCount': budgetCount,
      'averageBudget': budgetCount > 0 ? totalBudget / budgetCount : 0.0,
      'period': period,
    };
  }
  
  /// Get yearly budget summary
  Future<Map<String, dynamic>> getYearlyBudgetSummary(String userId, int year) async {
    final allBudgets = await getAll(userId);
    final yearBudgets = allBudgets.where((b) => b.period.year == year).toList();
    
    final monthlyTotals = <int, double>{};
    for (final budget in yearBudgets) {
      final month = budget.period.month;
      monthlyTotals[month] = (monthlyTotals[month] ?? 0.0) + budget.amount;
    }
    
    final totalYearBudget = yearBudgets.fold<double>(0.0, (sum, b) => sum + b.amount);
    
    return {
      'year': year,
      'totalBudget': totalYearBudget,
      'monthlyTotals': monthlyTotals,
      'averageMonthlyBudget': monthlyTotals.isNotEmpty ? 
        monthlyTotals.values.reduce((a, b) => a + b) / monthlyTotals.length : 0.0,
    };
  }
  
  /// Check if budget exists for category and period
  Future<bool> budgetExists(String userId, String categoryId, DateTime period) async {
    final budget = await getBudgetForCategoryAndPeriod(userId, categoryId, period);
    return budget != null;
  }
  
  /// Delete budget for category and period
  Future<void> deleteBudgetForCategoryAndPeriod(String userId, String categoryId, DateTime period) async {
    final budget = await getBudgetForCategoryAndPeriod(userId, categoryId, period);
    if (budget != null) {
      await delete(budget.id);
    }
  }
}
