import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:hive/hive.dart';
// TODO: Re-enable repositories after fixing naming conflicts
// import '../../data/repositories/transaction_repository.dart';
// import '../../data/repositories/category_repository.dart';
// import '../../data/repositories/loan_repository.dart';
// import '../../data/repositories/budget_repository.dart';
import '../config/supabase_config.dart';
import 'supabase_service.dart';

/// Service to handle bidirectional synchronization between local and remote data
class SyncService extends ChangeNotifier {
  static SyncService? _instance;
  static SyncService get instance => _instance ??= SyncService._();

  SyncService._();

  // TODO: Re-enable repositories after fixing naming conflicts
  // late TransactionRepository _transactionRepo;
  // late CategoryRepository _categoryRepo;
  // late LoanRepository _loanRepo;
  // late BudgetRepository _budgetRepo;

  // Sync state
  bool _isSyncing = false;
  DateTime? _lastSyncTime;
  Timer? _syncTimer;
  String? _currentUserId;

  // Getters
  bool get isSyncing => _isSyncing;
  DateTime? get lastSyncTime => _lastSyncTime;
  bool get isInitialized => _currentUserId != null;

  /// Initialize sync service with repositories
  Future<void> initialize(String userId) async {
    _currentUserId = userId;

    // TODO: Initialize repositories after fixing naming conflicts
    // _transactionRepo = TransactionRepository(Hive.box('transactions'));
    // _categoryRepo = CategoryRepository(Hive.box('categories'));
    // _loanRepo = LoanRepository(Hive.box('loans'));
    // _budgetRepo = BudgetRepository(Hive.box('budgets'));

    // Load last sync time
    final settingsBox = Hive.box('settings');
    final lastSyncString = settingsBox.get(SupabaseConfig.lastSyncKey);
    if (lastSyncString != null) {
      _lastSyncTime = DateTime.parse(lastSyncString);
    }

    // Start periodic sync if online
    _startPeriodicSync();

    debugPrint('SyncService initialized for user: $userId');
  }

  /// Start periodic synchronization
  void _startPeriodicSync() {
    _syncTimer?.cancel();

    if (SupabaseService.instance.isOnline) {
      _syncTimer = Timer.periodic(SupabaseConfig.syncInterval, (timer) {
        if (!_isSyncing && _currentUserId != null) {
          syncAll(_currentUserId!);
        }
      });
    }
  }

  /// Stop periodic synchronization
  void stopPeriodicSync() {
    _syncTimer?.cancel();
    _syncTimer = null;
  }

  /// Sync all data types
  Future<void> syncAll(String userId) async {
    if (_isSyncing || !SupabaseService.instance.isOnline) return;

    _isSyncing = true;
    notifyListeners();

    try {
      debugPrint('Starting full sync for user: $userId');

      // TODO: Re-enable after fixing repository naming conflicts
      // Sync in order of dependencies
      // await _categoryRepo.sync(userId);
      // await _loanRepo.sync(userId);
      // await _transactionRepo.sync(userId);
      // await _budgetRepo.sync(userId);

      // Update last sync time
      _lastSyncTime = DateTime.now();
      final settingsBox = Hive.box('settings');
      await settingsBox.put(
        SupabaseConfig.lastSyncKey,
        _lastSyncTime!.toIso8601String(),
      );

      debugPrint('Full sync completed successfully (repositories disabled)');
    } catch (e) {
      debugPrint('Sync failed: $e');
      rethrow;
    } finally {
      _isSyncing = false;
      notifyListeners();
    }
  }

  /// Sync specific data type
  Future<void> syncDataType(String userId, String dataType) async {
    if (_isSyncing || !SupabaseService.instance.isOnline) return;

    _isSyncing = true;
    notifyListeners();

    try {
      debugPrint('Syncing $dataType for user: $userId');

      // TODO: Re-enable after fixing repository naming conflicts
      switch (dataType.toLowerCase()) {
        case 'transactions':
          // await _transactionRepo.sync(userId);
          break;
        case 'categories':
          // await _categoryRepo.sync(userId);
          break;
        case 'loans':
          // await _loanRepo.sync(userId);
          break;
        case 'budgets':
          // await _budgetRepo.sync(userId);
          break;
        default:
          throw Exception('Unknown data type: $dataType');
      }

      debugPrint('$dataType sync completed (repositories disabled)');
    } catch (e) {
      debugPrint('$dataType sync failed: $e');
      rethrow;
    } finally {
      _isSyncing = false;
      notifyListeners();
    }
  }

  /// Force sync all data (ignores sync interval)
  Future<void> forceSyncAll(String userId) async {
    stopPeriodicSync();
    await syncAll(userId);
    _startPeriodicSync();
  }

  /// Get sync status for all data types
  Future<Map<String, dynamic>> getSyncStatus(String userId) async {
    try {
      // TODO: Re-enable after fixing repository naming conflicts
      // final transactionsPending = await _transactionRepo.getItemsNeedingSync(userId);
      // final categoriesPending = await _categoryRepo.getItemsNeedingSync(userId);
      // final loansPending = await _loanRepo.getItemsNeedingSync(userId);
      // final budgetsPending = await _budgetRepo.getItemsNeedingSync(userId);

      const totalPending = 0; // Temporarily disabled

      return {
        'isSyncing': _isSyncing,
        'lastSyncTime': _lastSyncTime?.toIso8601String(),
        'isOnline': SupabaseService.instance.isOnline,
        'totalPendingItems': totalPending,
        'pendingByType': {
          'transactions': 0,
          'categories': 0,
          'loans': 0,
          'budgets': 0,
        },
        'needsSync': totalPending > 0,
        'repositoriesDisabled': true,
      };
    } catch (e) {
      return {
        'error': e.toString(),
        'isSyncing': _isSyncing,
        'isOnline': SupabaseService.instance.isOnline,
      };
    }
  }

  /// Handle connectivity changes
  void onConnectivityChanged(bool isOnline) {
    if (isOnline && _currentUserId != null) {
      // Restart periodic sync when coming online
      _startPeriodicSync();

      // Trigger immediate sync if there are pending items
      Future.microtask(() async {
        final status = await getSyncStatus(_currentUserId!);
        if (status['needsSync'] == true) {
          syncAll(_currentUserId!);
        }
      });
    } else {
      // Stop periodic sync when offline
      stopPeriodicSync();
    }
  }

  /// Clear all local data (for logout/reset)
  Future<void> clearAllData() async {
    try {
      stopPeriodicSync();

      // Clear all boxes
      await Hive.box('transactions').clear();
      await Hive.box('categories').clear();
      await Hive.box('loans').clear();
      await Hive.box('budgets').clear();

      // Clear sync settings
      final settingsBox = Hive.box('settings');
      await settingsBox.delete(SupabaseConfig.lastSyncKey);

      _lastSyncTime = null;
      _currentUserId = null;

      debugPrint('All local data cleared');
    } catch (e) {
      debugPrint('Failed to clear local data: $e');
      rethrow;
    }
  }

  /// Export data for backup
  Future<Map<String, dynamic>> exportData(String userId) async {
    try {
      // TODO: Re-enable after fixing repository naming conflicts
      // final transactions = await _transactionRepo.getAll(userId);
      // final categories = await _categoryRepo.getAll(userId);
      // final loans = await _loanRepo.getAll(userId);
      // final budgets = await _budgetRepo.getAll(userId);

      return {
        'exportDate': DateTime.now().toIso8601String(),
        'userId': userId,
        'data': {
          'transactions': [],
          'categories': [],
          'loans': [],
          'budgets': [],
        },
        'repositoriesDisabled': true,
      };
    } catch (e) {
      throw Exception('Failed to export data: $e');
    }
  }

  /// Get conflict resolution suggestions
  Future<List<Map<String, dynamic>>> getConflicts(String userId) async {
    final conflicts = <Map<String, dynamic>>[];

    try {
      // TODO: Re-enable after fixing repository naming conflicts
      // Check for conflicts in all data types
      // final allTransactions = await _transactionRepo.getAll(userId);
      // final allCategories = await _categoryRepo.getAll(userId);
      // final allLoans = await _loanRepo.getAll(userId);
      // final allBudgets = await _budgetRepo.getAll(userId);

      // Temporarily return empty conflicts list
      return conflicts;
    } catch (e) {
      throw Exception('Failed to get conflicts: $e');
    }
  }

  @override
  void dispose() {
    stopPeriodicSync();
    super.dispose();
  }
}
