import 'package:hive/hive.dart';
import '../core/models/sync_status.dart';

part 'loan.g.dart';

@HiveType(typeId: 1)
class Loan extends HiveObject {
  @HiveField(0)
  late String id;

  @HiveField(1)
  late String name;

  @HiveField(2)
  late String type; // "بدهی" or "طلب"

  @HiveField(3)
  late String person;

  @HiveField(4)
  late double initialAmount;

  @HiveField(5)
  late DateTime startDate;

  @HiveField(6)
  late String status; // "فعال" or "تمام شده"

  @HiveField(7)
  late String userId;

  // Sync fields
  @HiveField(8)
  String? serverId;

  @HiveField(9)
  SyncStatus syncStatus = SyncStatus.pending;

  @HiveField(10)
  DateTime? lastSynced;

  @HiveField(11)
  late DateTime createdAt;

  @HiveField(12)
  late DateTime updatedAt;

  @HiveField(13)
  bool isDeleted = false;

  /// Convert to JSON for server sync
  Map<String, dynamic> toJson() {
    return {
      'id': serverId,
      'user_id': userId,
      'name': name,
      'type': type,
      'person': person,
      'initial_amount': initialAmount,
      'start_date': startDate.toIso8601String().split('T')[0], // Date only
      'status': status,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'last_synced': lastSynced?.toIso8601String(),
      'is_deleted': isDeleted,
    };
  }

  /// Update from server JSON
  void fromJson(Map<String, dynamic> json) {
    serverId = json['id'];
    userId = json['user_id'] ?? userId;
    name = json['name'] ?? name;
    type = json['type'] ?? type;
    person = json['person'] ?? person;
    initialAmount =
        (json['initial_amount'] as num?)?.toDouble() ?? initialAmount;
    if (json['start_date'] != null) {
      startDate = DateTime.parse(json['start_date']);
    }
    status = json['status'] ?? status;
    if (json['created_at'] != null) {
      createdAt = DateTime.parse(json['created_at']);
    }
    if (json['updated_at'] != null) {
      updatedAt = DateTime.parse(json['updated_at']);
    }
    if (json['last_synced'] != null) {
      lastSynced = DateTime.parse(json['last_synced']);
    }
    isDeleted = json['is_deleted'] ?? false;
  }

  /// Mark as synced with server
  void markAsSynced(String serverUuid) {
    serverId = serverUuid;
    syncStatus = SyncStatus.synced;
    lastSynced = DateTime.now();
  }

  /// Mark as pending sync
  void markAsPending() {
    syncStatus = SyncStatus.pending;
    updatedAt = DateTime.now();
  }

  /// Mark as having sync conflict
  void markAsConflict() {
    syncStatus = SyncStatus.conflict;
  }

  /// Mark as sync error
  void markAsError() {
    syncStatus = SyncStatus.error;
  }

  /// Check if needs sync
  bool get needsSync =>
      syncStatus == SyncStatus.pending || syncStatus == SyncStatus.error;

  /// Check if has conflict
  bool get hasConflict => syncStatus == SyncStatus.conflict;

  /// Soft delete the record
  void softDelete() {
    isDeleted = true;
    markAsPending();
  }
}
