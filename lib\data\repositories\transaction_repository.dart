import 'package:flutter/foundation.dart';
import 'package:hive/hive.dart';
import 'package:uuid/uuid.dart';
import '../../models/transaction.dart';
import '../../core/config/supabase_config.dart';
import '../../core/services/supabase_service.dart';
import 'base_repository.dart';

/// Repository for Transaction data with Local-First architecture
class TransactionRepository extends BaseRepositoryImpl<Transaction> {
  static final _uuid = Uuid();

  TransactionRepository(Box<Transaction> box)
    : super(box, SupabaseConfig.transactionsTable);

  @override
  Map<String, dynamic> toJson(Transaction item) => item.toJson();

  @override
  Transaction fromJson(Map<String, dynamic> json, String userId) {
    final transaction = Transaction()
      ..id = _uuid.v4()
      ..userId = userId
      ..createdAt = DateTime.now()
      ..updatedAt = DateTime.now();

    transaction.fromJson(json);
    return transaction;
  }

  @override
  String getUserId(Transaction item) => item.userId;

  @override
  String? getServerId(Transaction item) => item.serverId;

  @override
  void setServerId(Transaction item, String serverId) {
    item.serverId = serverId;
  }

  @override
  void markAsSynced(Transaction item, String serverId) {
    item.markAsSynced(serverId);
  }

  @override
  void markAsPending(Transaction item) {
    item.markAsPending();
  }

  @override
  bool needsSync(Transaction item) => item.needsSync;

  @override
  bool isDeleted(Transaction item) => item.isDeleted;

  @override
  void softDelete(Transaction item) {
    item.softDelete();
  }

  @override
  String getItemId(Transaction item) => item.id;

  @override
  void markAsError(Transaction item) {
    item.markAsError();
  }

  @override
  Future<void> sync(String userId) async {
    if (!SupabaseService.instance.isOnline) return;

    try {
      // 1. Push local changes to server
      await _pushLocalChanges(userId);

      // 2. Pull server changes to local
      await _pullServerChanges(userId);
    } catch (e) {
      throw Exception('Sync failed: $e');
    }
  }

  /// Push local changes to server
  Future<void> _pushLocalChanges(String userId) async {
    final itemsToSync = await getItemsNeedingSync(userId);

    for (final item in itemsToSync) {
      try {
        await syncItemToServer(item);
      } catch (e) {
        debugPrint('Failed to sync transaction ${item.id}: $e');
        // Continue with other items
      }
    }
  }

  /// Pull server changes to local
  Future<void> _pullServerChanges(String userId) async {
    try {
      final response = await supabase
          .from(tableName)
          .select()
          .eq('user_id', userId)
          .order('updated_at', ascending: false);

      for (final serverData in response) {
        await _mergeServerItem(serverData, userId);
      }
    } catch (e) {
      throw Exception('Failed to pull server changes: $e');
    }
  }

  /// Merge server item with local data
  Future<void> _mergeServerItem(
    Map<String, dynamic> serverData,
    String userId,
  ) async {
    final serverId = serverData['id'];

    // Find existing local item by server ID
    Transaction? localItem = localBox.values
        .where((item) => item.serverId == serverId)
        .firstOrNull;

    if (localItem == null) {
      // Create new local item from server data
      localItem = fromJson(serverData, userId);
      await localBox.put(localItem.id, localItem);
    } else {
      // Check for conflicts
      final serverUpdated = DateTime.parse(serverData['updated_at']);
      final localUpdated = localItem.updatedAt;

      if (serverUpdated.isAfter(localUpdated) || !localItem.needsSync) {
        // Server version is newer or no local changes, update local
        localItem.fromJson(serverData);
        localItem.markAsSynced(serverId);
        await localItem.save();
      } else if (localItem.needsSync) {
        // Conflict: both local and server have changes
        // For now, keep local changes and mark as conflict
        localItem.markAsConflict();
        await localItem.save();
      }
    }
  }

  /// Get transactions for a specific month
  Future<List<Transaction>> getTransactionsForMonth(
    String userId,
    DateTime month,
  ) async {
    final allTransactions = await getAll(userId);
    return allTransactions
        .where((t) => t.date.year == month.year && t.date.month == month.month)
        .toList();
  }

  /// Get transactions for a specific loan
  Future<List<Transaction>> getTransactionsForLoan(
    String userId,
    String loanId,
  ) async {
    final allTransactions = await getAll(userId);
    return allTransactions.where((t) => t.loanId == loanId).toList();
  }

  /// Get transactions by type (income/expense)
  Future<List<Transaction>> getTransactionsByType(
    String userId,
    String type,
  ) async {
    final allTransactions = await getAll(userId);
    return allTransactions.where((t) => t.type == type).toList();
  }

  /// Get transactions by category
  Future<List<Transaction>> getTransactionsByCategory(
    String userId,
    String categoryId,
  ) async {
    final allTransactions = await getAll(userId);
    return allTransactions.where((t) => t.categoryId == categoryId).toList();
  }

  /// Calculate total for transactions
  double calculateTotal(List<Transaction> transactions) {
    return transactions.fold<double>(0.0, (sum, t) => sum + t.amount);
  }

  /// Get income vs expense summary for a period
  Future<Map<String, double>> getIncomeExpenseSummary(
    String userId,
    DateTime startDate,
    DateTime endDate,
  ) async {
    final allTransactions = await getAll(userId);
    final periodTransactions = allTransactions
        .where(
          (t) =>
              t.date.isAfter(startDate.subtract(Duration(days: 1))) &&
              t.date.isBefore(endDate.add(Duration(days: 1))),
        )
        .toList();

    final income = calculateTotal(
      periodTransactions.where((t) => t.type == 'درآمد').toList(),
    );
    final expense = calculateTotal(
      periodTransactions.where((t) => t.type == 'مصرف').toList(),
    );

    return {'income': income, 'expense': expense, 'net': income - expense};
  }
}
