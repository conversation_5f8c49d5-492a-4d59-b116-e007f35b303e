/// Supabase configuration constants
class SupabaseConfig {
  // Supabase project credentials
  static const String supabaseUrl = 'https://wakwfevrgaqoqlmjsnzm.supabase.co';
  static const String supabaseAnonKey =
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Indha3dmZXZyZ2Fxb3FsbWpzbnptIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTM3MDE4NDcsImV4cCI6MjA2OTI3Nzg0N30.mf2eGuly9PGQPxdc8JkMxQwoLv2ftEgf6FhdBughGpI';

  // Database table names
  static const String usersTable = 'users';
  static const String transactionsTable = 'transactions';
  static const String categoriesTable = 'categories';
  static const String loansTable = 'loans';
  static const String budgetsTable = 'budgets';

  // Sync configuration
  static const Duration syncInterval = Duration(minutes: 5);
  static const Duration connectionTimeout = Duration(seconds: 30);
  static const int maxRetryAttempts = 3;

  // Local storage keys
  static const String lastSyncKey = 'last_sync_timestamp';
  static const String syncEnabledKey = 'sync_enabled';
  static const String offlineModeKey = 'offline_mode';
}
