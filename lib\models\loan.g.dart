// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'loan.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class LoanAdapter extends TypeAdapter<Loan> {
  @override
  final int typeId = 1;

  @override
  Loan read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Loan()
      ..id = fields[0] as String
      ..name = fields[1] as String
      ..type = fields[2] as String
      ..person = fields[3] as String
      ..initialAmount = fields[4] as double
      ..startDate = fields[5] as DateTime
      ..status = fields[6] as String
      ..userId = fields[7] as String
      ..serverId = fields[8] as String?
      ..syncStatus = fields[9] as SyncStatus
      ..lastSynced = fields[10] as DateTime?
      ..createdAt = fields[11] as DateTime
      ..updatedAt = fields[12] as DateTime
      ..isDeleted = fields[13] as bool;
  }

  @override
  void write(BinaryWriter writer, Loan obj) {
    writer
      ..writeByte(14)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.type)
      ..writeByte(3)
      ..write(obj.person)
      ..writeByte(4)
      ..write(obj.initialAmount)
      ..writeByte(5)
      ..write(obj.startDate)
      ..writeByte(6)
      ..write(obj.status)
      ..writeByte(7)
      ..write(obj.userId)
      ..writeByte(8)
      ..write(obj.serverId)
      ..writeByte(9)
      ..write(obj.syncStatus)
      ..writeByte(10)
      ..write(obj.lastSynced)
      ..writeByte(11)
      ..write(obj.createdAt)
      ..writeByte(12)
      ..write(obj.updatedAt)
      ..writeByte(13)
      ..write(obj.isDeleted);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is LoanAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
