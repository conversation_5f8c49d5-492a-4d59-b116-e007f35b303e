import 'package:hive/hive.dart';
import '../core/models/sync_status.dart';

part 'budget.g.dart';

@HiveType(typeId: 4)
class Budget extends HiveObject {
  @HiveField(0)
  late String id;

  @HiveField(1)
  late DateTime period;

  @HiveField(2)
  late double amount;

  @HiveField(3)
  late String categoryId;

  @HiveField(4)
  late String userId;

  // Sync fields
  @HiveField(5)
  String? serverId;

  @HiveField(6)
  SyncStatus syncStatus = SyncStatus.pending;

  @HiveField(7)
  DateTime? lastSynced;

  @HiveField(8)
  late DateTime createdAt;

  @HiveField(9)
  late DateTime updatedAt;

  @HiveField(10)
  bool isDeleted = false;

  /// Convert to JSON for server sync
  Map<String, dynamic> toJson() {
    return {
      'id': serverId,
      'user_id': userId,
      'category_id': categoryId,
      'period': period.toIso8601String().split('T')[0], // Date only
      'amount': amount,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'last_synced': lastSynced?.toIso8601String(),
      'is_deleted': isDeleted,
    };
  }

  /// Update from server JSON
  void fromJson(Map<String, dynamic> json) {
    serverId = json['id'];
    userId = json['user_id'] ?? userId;
    categoryId = json['category_id'] ?? categoryId;
    if (json['period'] != null) {
      period = DateTime.parse(json['period']);
    }
    amount = (json['amount'] as num?)?.toDouble() ?? amount;
    if (json['created_at'] != null) {
      createdAt = DateTime.parse(json['created_at']);
    }
    if (json['updated_at'] != null) {
      updatedAt = DateTime.parse(json['updated_at']);
    }
    if (json['last_synced'] != null) {
      lastSynced = DateTime.parse(json['last_synced']);
    }
    isDeleted = json['is_deleted'] ?? false;
  }

  /// Mark as synced with server
  void markAsSynced(String serverUuid) {
    serverId = serverUuid;
    syncStatus = SyncStatus.synced;
    lastSynced = DateTime.now();
  }

  /// Mark as pending sync
  void markAsPending() {
    syncStatus = SyncStatus.pending;
    updatedAt = DateTime.now();
  }

  /// Mark as having sync conflict
  void markAsConflict() {
    syncStatus = SyncStatus.conflict;
  }

  /// Mark as sync error
  void markAsError() {
    syncStatus = SyncStatus.error;
  }

  /// Check if needs sync
  bool get needsSync =>
      syncStatus == SyncStatus.pending || syncStatus == SyncStatus.error;

  /// Check if has conflict
  bool get hasConflict => syncStatus == SyncStatus.conflict;

  /// Soft delete the record
  void softDelete() {
    isDeleted = true;
    markAsPending();
  }
}
